# الرصد الإعلامي

موقع ويب لإدارة ومتابعة الأخبار والمحتوى الإعلامي بدون الحاجة لخادم خارجي.

## المميزات

### 🔧 بدون خادم خارجي
- لا يحتاج إلى XAMPP أو SQL Server
- يعمل مباشرة في المتصفح
- حفظ البيانات محلياً باستخدام Local Storage

### 📰 إدارة الأخبار
- إضافة أخبار جديدة
- تحرير الأخبار الموجودة
- حذف الأخبار
- تصنيف الأخبار حسب المصدر والموضوع
- ربط الأخبار بالمصادر المحفوظة

### 🏢 إدارة المصادر
- إضافة وتحرير مصادر الأخبار
- تصنيف المصادر حسب النوع (صحيفة، موقع، قناة تلفزيونية، إلخ)
- تتبع حالة المصادر (نشط/غير نشط)
- إحصائيات المصادر وعدد الأخبار لكل مصدر

### 🔍 البحث والفلترة
- البحث في عناوين ومحتوى الأخبار
- فلترة حسب التصنيف
- فلترة حسب المصدر
- فلترة حسب التاريخ
- البحث في الكلمات المفتاحية

### 📊 الإحصائيات
- إجمالي عدد الأخبار
- عدد أخبار اليوم
- عدد التصنيفات المختلفة

### 🎨 واجهة المستخدم
- تصميم عصري ومتجاوب
- دعم اللغة العربية
- سهولة الاستخدام
- أيقونات تفاعلية

## كيفية الاستخدام

### 1. تشغيل الموقع
- افتح ملف `index.html` في أي متصفح ويب
- لا تحتاج لتثبيت أي برامج إضافية

### 2. إضافة خبر جديد
1. اضغط على زر "إضافة خبر جديد"
2. املأ البيانات المطلوبة:
   - عنوان الخبر (مطلوب)
   - محتوى الخبر (مطلوب)
   - المصدر (مطلوب)
   - التصنيف (مطلوب)
   - تاريخ ووقت النشر
   - رابط الخبر (اختياري)
   - الكلمات المفتاحية (اختياري)
3. اضغط "حفظ"

### 3. البحث والفلترة
- استخدم مربع البحث للبحث في النصوص
- اختر التصنيف أو المصدر من القوائم المنسدلة
- حدد تاريخ معين للفلترة
- اضغط "مسح الفلاتر" لإزالة جميع الفلاتر

### 4. تحرير الأخبار
- اضغط على زر "تحرير" في أي خبر
- عدّل البيانات المطلوبة
- اضغط "حفظ"

### 5. حذف الأخبار
- اضغط على زر "حذف" في أي خبر
- أكد عملية الحذف

### 6. إدارة المصادر
1. اضغط على "إدارة المصادر" في الصفحة الرئيسية
2. لإضافة مصدر جديد:
   - اضغط "إضافة مصدر جديد"
   - املأ البيانات (الاسم والنوع مطلوبان)
   - اختر حالة المصدر (نشط/غير نشط)
3. لتحرير مصدر: اضغط "تحرير" في بطاقة المصدر
4. لحذف مصدر: اضغط "حذف" (سيتم التحذير إذا كان له أخبار مرتبطة)

### 7. ربط الأخبار بالمصادر
- عند إضافة خبر جديد، يمكنك اختيار المصدر من القائمة المنسدلة
- أو كتابة اسم المصدر يدوياً
- المصادر النشطة فقط تظهر في القائمة المنسدلة

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحة
- **CSS3**: التنسيق والتصميم
- **JavaScript**: المنطق والتفاعل
- **Local Storage**: حفظ البيانات محلياً
- **Font Awesome**: الأيقونات

## ملاحظات مهمة

### حفظ البيانات
- البيانات محفوظة في متصفحك محلياً
- لن تفقد البيانات عند إغلاق المتصفح
- قد تفقد البيانات عند مسح بيانات المتصفح

### النسخ الاحتياطي
- لعمل نسخة احتياطية: انسخ محتوى Local Storage
- أو قم بتصدير البيانات يدوياً

### التوافق
- يعمل على جميع المتصفحات الحديثة
- متوافق مع الهواتف والأجهزة اللوحية
- لا يحتاج اتصال بالإنترنت بعد التحميل الأول

## الاستخدام المتقدم

### إضافة مميزات جديدة
يمكنك تطوير الموقع بإضافة:
- تصدير البيانات إلى Excel/CSV
- استيراد البيانات من ملفات
- إضافة الصور للأخبار
- نظام تقييم الأخبار
- إشعارات للأخبار الجديدة

### التخصيص
- عدّل ملف `style.css` لتغيير التصميم
- عدّل ملف `script.js` لإضافة وظائف جديدة
- عدّل ملف `index.html` لتغيير هيكل الصفحة

## الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تأكد من تفعيل JavaScript في المتصفح
2. تأكد من دعم المتصفح لـ Local Storage
3. جرب تحديث الصفحة
4. جرب مسح cache المتصفح

---

**تم تطوير هذا الموقع ليكون بسيطاً وفعالاً لإدارة الأخبار بدون تعقيدات تقنية**
