* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

header h1 i {
    margin-left: 10px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Stats Section */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card i {
    font-size: 2rem;
    color: #667eea;
}

.stat-card h3 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 5px;
}

.stat-card p {
    color: #666;
    font-size: 0.9rem;
}

/* Filters Section */
.filters {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.search-box {
    position: relative;
    margin-bottom: 15px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 5px;
    font-size: 16px;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: center;
}

.filter-controls select,
.filter-controls input[type="date"] {
    padding: 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 5px;
    font-size: 14px;
}

/* News List */
.news-list {
    margin-bottom: 30px;
}

.news-item {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.news-item:hover {
    transform: translateY(-2px);
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.news-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.4;
}

.news-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 15px;
}

.news-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.news-content {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
}

.news-tags {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.tag {
    background-color: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.8rem;
}

.news-actions {
    display: flex;
    gap: 10px;
}

.no-news {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-news i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
}

.modal-header h2 {
    color: #333;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #333;
}

/* Form */
form {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 5px;
    font-size: 14px;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.source-input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.source-input-group select {
    margin-bottom: 0;
}

/* Sources Styles */
.sources-list {
    margin-bottom: 30px;
}

.source-item {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.source-item:hover {
    transform: translateY(-2px);
}

.source-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.source-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.source-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: #666;
    flex-wrap: wrap;
}

.source-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.source-stats {
    text-align: center;
}

.news-count {
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 5px;
}

.source-description {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
    font-style: italic;
}

.source-contact,
.source-notes {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.source-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.status-active {
    color: #28a745 !important;
}

.status-inactive {
    color: #dc3545 !important;
}

.no-sources {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-sources i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

/* Navigation improvements */
header nav {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Responsive */
@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-controls select,
    .filter-controls input[type="date"] {
        width: 100%;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .news-header,
    .source-header {
        flex-direction: column;
        gap: 10px;
    }

    .news-actions,
    .source-actions {
        justify-content: center;
    }

    .source-meta {
        justify-content: center;
    }

    header nav {
        flex-direction: column;
        gap: 8px;
    }
}
