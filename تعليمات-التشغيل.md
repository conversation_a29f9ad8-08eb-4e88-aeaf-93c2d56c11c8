# 🚀 تعليمات تشغيل نظام الزحف التلقائي

## 📋 المتطلبات:
1. **Node.js** - حمل من [nodejs.org](https://nodejs.org)
2. **متصفح حديث** - Chrome أو Firefox
3. **اتصال بالإنترنت**

## ⚡ خطوات التشغيل السريع:

### 1. تثبيت Node.js:
- حمل Node.js من الموقع الرسمي
- ثبته على جهازك
- تأكد من التثبيت بفتح Command Prompt وكتابة:
```bash
node --version
npm --version
```

### 2. تثبيت المكتبات:
افتح Command Prompt في مجلد المشروع واكتب:
```bash
npm install
```

### 3. تشغيل خادم الزحف:
```bash
npm start
```
أو:
```bash
node server.js
```

### 4. التأكد من عمل الخادم:
- افتح المتصفح واذهب إلى: `http://localhost:3000`
- يجب أن ترى صفحة تأكيد عمل الخادم

### 5. استخدام الموقع:
- افتح `index.html` في المتصفح
- اضغط على زر "جلب الأخبار تلقائياً"
- انتظر حتى يتم جلب الأخبار

## 🎯 كيفية العمل:

### الزحف التلقائي:
1. **اضغط "جلب الأخبار تلقائياً"**
2. **سيبدأ النظام بزحف المواقع التالية:**
   - شفق نيوز
   - السومرية نيوز  
   - بغداد اليوم
   - وكالة المعلومة

3. **سيتم استخراج:**
   - عناوين الأخبار
   - المحتوى المختصر
   - روابط الأخبار
   - تاريخ ووقت الجلب

4. **سيتم إضافة الأخبار تلقائياً للموقع**

## 🔧 إعدادات متقدمة:

### إضافة مصادر جديدة:
عدل ملف `scraper.js` وأضف مصدر جديد:
```javascript
{
    name: 'اسم المصدر',
    url: 'https://example.com',
    type: 'puppeteer',
    selectors: {
        articles: '.news-item',
        title: 'h2',
        content: '.summary',
        link: 'a'
    }
}
```

### تغيير فترة الزحف التلقائي:
في ملف `server.js` غير هذا السطر:
```javascript
// كل ساعة
cron.schedule('0 * * * *', async () => {

// كل 30 دقيقة  
cron.schedule('*/30 * * * *', async () => {

// كل 10 دقائق
cron.schedule('*/10 * * * *', async () => {
```

## 🛠️ حل المشاكل الشائعة:

### المشكلة: "تعذر الاتصال بخادم الزحف"
**الحل:**
1. تأكد من تشغيل `node server.js`
2. تأكد من أن الخادم يعمل على `localhost:3000`
3. تأكد من عدم حظر Firewall للاتصال

### المشكلة: "لم يتم جلب أخبار"
**الحل:**
1. تحقق من اتصال الإنترنت
2. بعض المواقع قد تحظر الزحف
3. جرب زحف مصدر واحد أولاً

### المشكلة: خطأ في تثبيت المكتبات
**الحل:**
```bash
# مسح المجلد وإعادة التثبيت
rm -rf node_modules
rm package-lock.json
npm install
```

### المشكلة: الخادم لا يعمل
**الحل:**
1. تأكد من إغلاق أي برنامج يستخدم Port 3000
2. غير الـ Port في `server.js`:
```javascript
const port = process.env.PORT || 3001; // غير إلى 3001
```

## 📊 مراقبة الأداء:

### لوحة التحكم:
- اذهب إلى `http://localhost:3000`
- ستجد معلومات عن:
  - حالة الخادم
  - آخر عملية زحف
  - عدد المصادر
  - الأخبار المحفوظة

### APIs المتاحة:
- `GET /api/test` - اختبار الاتصال
- `GET /api/scrape-news` - جلب جميع الأخبار
- `GET /api/sources` - قائمة المصادر
- `GET /api/cached-news` - الأخبار المحفوظة

## ⚖️ ملاحظات قانونية:

### احترام شروط الاستخدام:
- تأكد من احترام شروط المواقع
- لا تفرط في الطلبات
- استخدم فواصل زمنية مناسبة

### الاستخدام الأخلاقي:
- لا تستخدم النظام لأغراض تجارية بدون إذن
- احترم حقوق الطبع والنشر
- اذكر المصدر عند النقل

## 🚀 نشر النظام:

### على Heroku (مجاني):
1. أنشئ حساب على Heroku
2. ثبت Heroku CLI
3. اتبع التعليمات:
```bash
heroku create your-app-name
git push heroku main
```

### على Vercel (مجاني):
1. أنشئ حساب على Vercel
2. اربط مع GitHub
3. انشر المشروع تلقائياً

## 📞 الدعم:
إذا واجهت أي مشاكل:
1. تحقق من console في المتصفح (F12)
2. تحقق من logs الخادم
3. تأكد من تحديث المكتبات

---
**نظام الزحف التلقائي للأخبار العراقية - جاهز للاستخدام! 🎉**
