// تطبيق إدارة المصادر
class SourcesManager {
    constructor() {
        this.sources = JSON.parse(localStorage.getItem('sourcesData')) || this.getDefaultSources();
        this.news = JSON.parse(localStorage.getItem('newsData')) || [];
        this.currentEditId = null;

        // حفظ المصادر الافتراضية إذا لم تكن موجودة
        if (!localStorage.getItem('sourcesData')) {
            this.saveData();
        }

        this.init();
    }

    getDefaultSources() {
        const now = new Date().toISOString();
        return [
            {
                id: 2,
                name: "الفرات نيوز",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://alforatnews.iq/news/iraq",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 3,
                name: "شفق نيوز",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://shafaq.com/ar",
                description: "وكالة أنباء عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 4,
                name: "السومرية نيوز",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://www.alsumaria.tv/iraq-news/48/%D9%85%D8%AD%D9%84%D9%8A%D8%A7%D8%AA",
                description: "قناة السومرية الإخبارية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 6,
                name: "الجبال نيوز",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://aljeebal.com/",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 7,
                name: "المربد",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://www.al-mirbad.com/Home",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 8,
                name: "ميل نيوز",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://miliq.news/",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 9,
                name: "قناة الرشيد",
                type: "قناة تلفزيونية",
                country: "العراق",
                website: "https://www.alrasheedmedia.com/",
                description: "قناة تلفزيونية عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 10,
                name: "تايتل",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://title.news/local/",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 11,
                name: "IQ News",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://www.iqiraq.news/lastnews",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 12,
                name: "نون الخبرية",
                type: "وكالة أنباء",
                country: "العراق",
                website: "https://non14.net/local",
                description: "وكالة أنباء عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 15,
                name: "وكالة المعلومة",
                type: "وكالة أنباء",
                country: "العراق",
                website: "https://www.almaalomah.me/",
                description: "وكالة أنباء عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 16,
                name: "وكالة نون الخبرية",
                type: "وكالة أنباء",
                country: "العراق",
                website: "https://www.non14.net/",
                description: "وكالة أنباء عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 17,
                name: "وكالة أخبار العراق",
                type: "وكالة أنباء",
                country: "العراق",
                website: "https://www.iraqakhbar.com/",
                description: "وكالة أنباء عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 18,
                name: "كلمة",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://kalimaiq.com/news/1",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 20,
                name: "قناة الفلوجة",
                type: "قناة تلفزيونية",
                country: "العراق",
                website: "https://alfallujah.tv/category/news/",
                description: "قناة تلفزيونية عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 22,
                name: "قناة التغيير",
                type: "قناة تلفزيونية",
                country: "العراق",
                website: "https://altaghier.tv/archives/category/%d8%a7%d8%ae%d8%a8%d8%a7%d8%b1/",
                description: "قناة تلفزيونية عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 23,
                name: "صحيفة المدى",
                type: "صحيفة",
                country: "العراق",
                website: "https://almadapaper.net/category/%d9%85%d8%ad%d9%84%d9%8a%d8%a7%d8%aa/",
                description: "صحيفة عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 24,
                name: "صحيفة الزمان",
                type: "صحيفة",
                country: "العراق",
                website: "https://www.azzaman.com/",
                description: "صحيفة عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 25,
                name: "UTV",
                type: "قناة تلفزيونية",
                country: "العراق",
                website: "https://utviraq.net/news/",
                description: "قناة تلفزيونية عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 26,
                name: "المسرى",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://almasra.iq/category/%d8%a7%d9%84%d8%b9%d8%b1%d8%a7%d9%82/",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            // باقي المصادر
            ...this.getAdditionalSources()
        ];
    }

    getAdditionalSources() {
        const now = new Date().toISOString();
        return [
            {
                id: 27,
                name: "صحيفة الدستور",
                type: "صحيفة",
                country: "العراق",
                website: "https://www.addustour.com/",
                description: "صحيفة عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 28,
                name: "المعلومة",
                type: "وكالة أنباء",
                country: "العراق",
                website: "https://almaalomah.me/",
                description: "وكالة أنباء عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 30,
                name: "صحيفة الصباح الجديد",
                type: "صحيفة",
                country: "العراق",
                website: "https://www.newsabah.com/",
                description: "صحيفة عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 32,
                name: "بغداد اليوم",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://baghdadtoday.news/lastnews",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 29,
                name: "قناة الرابعة",
                type: "قناة تلفزيونية",
                country: "العراق",
                website: "https://alrabiaa.tv/",
                description: "قناة تلفزيونية عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 31,
                name: "ناس نيوز",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://www.nasnews.com/",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 36,
                name: "كتابات في الميزان",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://kitabat.com/",
                description: "موقع إخباري وثقافي عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 40,
                name: "المسلة",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://almasalah.com/",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 42,
                name: "موسوعة الرافدين",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://www.alrafidain.news/News/Category/1/%D8%A7%D9%84%D8%B9%D8%B1%D8%A7%D9%82",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 49,
                name: "وكالة اليوم",
                type: "وكالة أنباء",
                country: "العراق",
                website: "https://today-agency.net/News/8/%D9%85%D8%AD%D9%84%D9%8A",
                description: "وكالة أنباء عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 50,
                name: "شبكة الساعة",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://alssaa.com/posts/all",
                description: "شبكة إخبارية عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 51,
                name: "الجبال",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://aljeebal.com/posts",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 52,
                name: "قناة الاولى",
                type: "قناة تلفزيونية",
                country: "العراق",
                website: "https://alawla.tv/local/",
                description: "قناة تلفزيونية عراقية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 59,
                name: "شبكة اخبار الناصرية",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://nasiriyah.org/ar/post/category/allnews/",
                description: "شبكة إخبارية محلية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 60,
                name: "عراق اوبزيرفر",
                type: "موقع إلكتروني",
                country: "العراق",
                website: "https://observeriraq.net/category/%d8%a7%d9%84%d8%b9%d8%b1%d8%a7%d9%82/",
                description: "موقع إخباري عراقي",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            },
            {
                id: 61,
                name: "NRT عربية",
                type: "قناة تلفزيونية",
                country: "العراق",
                website: "https://www.nrttv.com/ar/Babetekan.aspx?MapID=3",
                description: "قناة تلفزيونية كردية - عربية",
                language: "العربية",
                status: "نشط",
                contact: "",
                notes: "",
                createdAt: now,
                updatedAt: now
            }
        ];
    }

    init() {
        this.bindEvents();
        this.updateStats();
        this.displaySources();
    }

    bindEvents() {
        // أحداث الأزرار والنماذج
        document.getElementById('addSourceBtn').addEventListener('click', () => this.openModal());
        document.getElementById('sourceForm').addEventListener('submit', (e) => this.handleSubmit(e));
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        document.querySelector('.close').addEventListener('click', () => this.closeModal());
        
        // أحداث البحث والفلترة
        document.getElementById('searchInput').addEventListener('input', () => this.filterSources());
        document.getElementById('typeFilter').addEventListener('change', () => this.filterSources());
        document.getElementById('statusFilter').addEventListener('change', () => this.filterSources());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearFilters());

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('sourceModal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    openModal(sourceId = null) {
        const modal = document.getElementById('sourceModal');
        const modalTitle = document.getElementById('modalTitle');
        const form = document.getElementById('sourceForm');
        
        if (sourceId) {
            // تحرير مصدر موجود
            const source = this.sources.find(s => s.id === sourceId);
            if (source) {
                modalTitle.textContent = 'تحرير المصدر';
                this.currentEditId = sourceId;
                this.fillForm(source);
            }
        } else {
            // إضافة مصدر جديد
            modalTitle.textContent = 'إضافة مصدر جديد';
            this.currentEditId = null;
            form.reset();
            // تعيين القيم الافتراضية
            document.getElementById('sourceLanguage').value = 'العربية';
            document.getElementById('sourceStatus').value = 'نشط';
        }
        
        modal.style.display = 'block';
    }

    closeModal() {
        document.getElementById('sourceModal').style.display = 'none';
        document.getElementById('sourceForm').reset();
        this.currentEditId = null;
    }

    fillForm(source) {
        document.getElementById('sourceName').value = source.name;
        document.getElementById('sourceType').value = source.type;
        document.getElementById('sourceCountry').value = source.country || '';
        document.getElementById('sourceWebsite').value = source.website || '';
        document.getElementById('sourceDescription').value = source.description || '';
        document.getElementById('sourceLanguage').value = source.language || 'العربية';
        document.getElementById('sourceStatus').value = source.status || 'نشط';
        document.getElementById('sourceContact').value = source.contact || '';
        document.getElementById('sourceNotes').value = source.notes || '';
    }

    handleSubmit(e) {
        e.preventDefault();
        
        const formData = {
            name: document.getElementById('sourceName').value.trim(),
            type: document.getElementById('sourceType').value,
            country: document.getElementById('sourceCountry').value.trim(),
            website: document.getElementById('sourceWebsite').value.trim(),
            description: document.getElementById('sourceDescription').value.trim(),
            language: document.getElementById('sourceLanguage').value,
            status: document.getElementById('sourceStatus').value,
            contact: document.getElementById('sourceContact').value.trim(),
            notes: document.getElementById('sourceNotes').value.trim()
        };

        // التحقق من عدم تكرار اسم المصدر
        const existingSource = this.sources.find(s => 
            s.name.toLowerCase() === formData.name.toLowerCase() && 
            s.id !== this.currentEditId
        );

        if (existingSource) {
            this.showNotification('يوجد مصدر بنفس الاسم مسبقاً', 'error');
            return;
        }

        if (this.currentEditId) {
            // تحديث مصدر موجود
            const index = this.sources.findIndex(s => s.id === this.currentEditId);
            if (index !== -1) {
                this.sources[index] = { 
                    ...this.sources[index], 
                    ...formData,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // إضافة مصدر جديد
            const newSource = {
                id: Date.now(),
                ...formData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            this.sources.unshift(newSource);
        }

        this.saveData();
        this.updateStats();
        this.displaySources();
        this.closeModal();
        
        this.showNotification(
            this.currentEditId ? 'تم تحديث المصدر بنجاح' : 'تم إضافة المصدر بنجاح'
        );
    }

    deleteSource(id) {
        const source = this.sources.find(s => s.id === id);
        if (!source) return;

        // التحقق من وجود أخبار مرتبطة بهذا المصدر
        const relatedNews = this.news.filter(n => n.source === source.name);
        
        let confirmMessage = `هل أنت متأكد من حذف المصدر "${source.name}"؟`;
        if (relatedNews.length > 0) {
            confirmMessage += `\n\nتحذير: يوجد ${relatedNews.length} خبر مرتبط بهذا المصدر.`;
        }

        if (confirm(confirmMessage)) {
            this.sources = this.sources.filter(s => s.id !== id);
            this.saveData();
            this.updateStats();
            this.displaySources();
            this.showNotification('تم حذف المصدر بنجاح');
        }
    }

    saveData() {
        localStorage.setItem('sourcesData', JSON.stringify(this.sources));
    }

    updateStats() {
        const activeSources = this.sources.filter(s => s.status === 'نشط');
        const sourcesWithNews = this.sources.filter(s => 
            this.news.some(n => n.source === s.name)
        );

        document.getElementById('totalSources').textContent = this.sources.length;
        document.getElementById('activeSources').textContent = activeSources.length;
        document.getElementById('sourcesWithNews').textContent = sourcesWithNews.length;
    }

    displaySources(sourcesToShow = this.sources) {
        const container = document.getElementById('sourcesContainer');
        
        if (sourcesToShow.length === 0) {
            container.innerHTML = `
                <div class="no-sources">
                    <i class="fas fa-building"></i>
                    <p>لا توجد مصادر تطابق معايير البحث</p>
                </div>
            `;
            return;
        }

        container.innerHTML = sourcesToShow.map(source => this.createSourceCard(source)).join('');
    }

    createSourceCard(source) {
        const newsCount = this.news.filter(n => n.source === source.name).length;
        const statusClass = source.status === 'نشط' ? 'status-active' : 'status-inactive';
        
        const websiteHtml = source.website 
            ? `<a href="${source.website}" target="_blank" class="btn btn-secondary btn-sm">
                <i class="fas fa-external-link-alt"></i> زيارة الموقع
               </a>`
            : '';

        return `
            <div class="source-item">
                <div class="source-header">
                    <div class="source-info">
                        <h3 class="source-name">${source.name}</h3>
                        <div class="source-meta">
                            <span class="source-type"><i class="fas fa-tag"></i> ${source.type}</span>
                            ${source.country ? `<span class="source-country"><i class="fas fa-globe"></i> ${source.country}</span>` : ''}
                            <span class="source-language"><i class="fas fa-language"></i> ${source.language}</span>
                            <span class="source-status ${statusClass}">
                                <i class="fas ${source.status === 'نشط' ? 'fa-check-circle' : 'fa-times-circle'}"></i> 
                                ${source.status}
                            </span>
                        </div>
                    </div>
                    <div class="source-stats">
                        <div class="news-count">
                            <i class="fas fa-newspaper"></i>
                            <span>${newsCount} خبر</span>
                        </div>
                    </div>
                </div>
                
                ${source.description ? `<div class="source-description">${source.description}</div>` : ''}
                
                ${source.contact ? `
                    <div class="source-contact">
                        <i class="fas fa-phone"></i> ${source.contact}
                    </div>
                ` : ''}
                
                ${source.notes ? `
                    <div class="source-notes">
                        <i class="fas fa-sticky-note"></i> ${source.notes}
                    </div>
                ` : ''}
                
                <div class="source-actions">
                    ${websiteHtml}
                    <button onclick="sourcesManager.openModal(${source.id})" class="btn btn-secondary btn-sm">
                        <i class="fas fa-edit"></i> تحرير
                    </button>
                    <button onclick="sourcesManager.deleteSource(${source.id})" class="btn btn-danger btn-sm">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
    }

    filterSources() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('typeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        const filtered = this.sources.filter(source => {
            const matchesSearch = !searchTerm || 
                source.name.toLowerCase().includes(searchTerm) ||
                (source.description && source.description.toLowerCase().includes(searchTerm)) ||
                (source.country && source.country.toLowerCase().includes(searchTerm));
            
            const matchesType = !typeFilter || source.type === typeFilter;
            const matchesStatus = !statusFilter || source.status === statusFilter;

            return matchesSearch && matchesType && matchesStatus;
        });

        this.displaySources(filtered);
    }

    clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('typeFilter').value = '';
        document.getElementById('statusFilter').value = '';
        this.displaySources();
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        const bgColor = type === 'error' ? '#dc3545' : '#4CAF50';
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            max-width: 300px;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 4000);
    }
}

// تشغيل التطبيق
const sourcesManager = new SourcesManager();
