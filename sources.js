// تطبيق إدارة المصادر
class SourcesManager {
    constructor() {
        this.sources = JSON.parse(localStorage.getItem('sourcesData')) || [];
        this.news = JSON.parse(localStorage.getItem('newsData')) || [];
        this.currentEditId = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateStats();
        this.displaySources();
    }

    bindEvents() {
        // أحداث الأزرار والنماذج
        document.getElementById('addSourceBtn').addEventListener('click', () => this.openModal());
        document.getElementById('sourceForm').addEventListener('submit', (e) => this.handleSubmit(e));
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        document.querySelector('.close').addEventListener('click', () => this.closeModal());
        
        // أحداث البحث والفلترة
        document.getElementById('searchInput').addEventListener('input', () => this.filterSources());
        document.getElementById('typeFilter').addEventListener('change', () => this.filterSources());
        document.getElementById('statusFilter').addEventListener('change', () => this.filterSources());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearFilters());

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('sourceModal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    openModal(sourceId = null) {
        const modal = document.getElementById('sourceModal');
        const modalTitle = document.getElementById('modalTitle');
        const form = document.getElementById('sourceForm');
        
        if (sourceId) {
            // تحرير مصدر موجود
            const source = this.sources.find(s => s.id === sourceId);
            if (source) {
                modalTitle.textContent = 'تحرير المصدر';
                this.currentEditId = sourceId;
                this.fillForm(source);
            }
        } else {
            // إضافة مصدر جديد
            modalTitle.textContent = 'إضافة مصدر جديد';
            this.currentEditId = null;
            form.reset();
            // تعيين القيم الافتراضية
            document.getElementById('sourceLanguage').value = 'العربية';
            document.getElementById('sourceStatus').value = 'نشط';
        }
        
        modal.style.display = 'block';
    }

    closeModal() {
        document.getElementById('sourceModal').style.display = 'none';
        document.getElementById('sourceForm').reset();
        this.currentEditId = null;
    }

    fillForm(source) {
        document.getElementById('sourceName').value = source.name;
        document.getElementById('sourceType').value = source.type;
        document.getElementById('sourceCountry').value = source.country || '';
        document.getElementById('sourceWebsite').value = source.website || '';
        document.getElementById('sourceDescription').value = source.description || '';
        document.getElementById('sourceLanguage').value = source.language || 'العربية';
        document.getElementById('sourceStatus').value = source.status || 'نشط';
        document.getElementById('sourceContact').value = source.contact || '';
        document.getElementById('sourceNotes').value = source.notes || '';
    }

    handleSubmit(e) {
        e.preventDefault();
        
        const formData = {
            name: document.getElementById('sourceName').value.trim(),
            type: document.getElementById('sourceType').value,
            country: document.getElementById('sourceCountry').value.trim(),
            website: document.getElementById('sourceWebsite').value.trim(),
            description: document.getElementById('sourceDescription').value.trim(),
            language: document.getElementById('sourceLanguage').value,
            status: document.getElementById('sourceStatus').value,
            contact: document.getElementById('sourceContact').value.trim(),
            notes: document.getElementById('sourceNotes').value.trim()
        };

        // التحقق من عدم تكرار اسم المصدر
        const existingSource = this.sources.find(s => 
            s.name.toLowerCase() === formData.name.toLowerCase() && 
            s.id !== this.currentEditId
        );

        if (existingSource) {
            this.showNotification('يوجد مصدر بنفس الاسم مسبقاً', 'error');
            return;
        }

        if (this.currentEditId) {
            // تحديث مصدر موجود
            const index = this.sources.findIndex(s => s.id === this.currentEditId);
            if (index !== -1) {
                this.sources[index] = { 
                    ...this.sources[index], 
                    ...formData,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // إضافة مصدر جديد
            const newSource = {
                id: Date.now(),
                ...formData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            this.sources.unshift(newSource);
        }

        this.saveData();
        this.updateStats();
        this.displaySources();
        this.closeModal();
        
        this.showNotification(
            this.currentEditId ? 'تم تحديث المصدر بنجاح' : 'تم إضافة المصدر بنجاح'
        );
    }

    deleteSource(id) {
        const source = this.sources.find(s => s.id === id);
        if (!source) return;

        // التحقق من وجود أخبار مرتبطة بهذا المصدر
        const relatedNews = this.news.filter(n => n.source === source.name);
        
        let confirmMessage = `هل أنت متأكد من حذف المصدر "${source.name}"؟`;
        if (relatedNews.length > 0) {
            confirmMessage += `\n\nتحذير: يوجد ${relatedNews.length} خبر مرتبط بهذا المصدر.`;
        }

        if (confirm(confirmMessage)) {
            this.sources = this.sources.filter(s => s.id !== id);
            this.saveData();
            this.updateStats();
            this.displaySources();
            this.showNotification('تم حذف المصدر بنجاح');
        }
    }

    saveData() {
        localStorage.setItem('sourcesData', JSON.stringify(this.sources));
    }

    updateStats() {
        const activeSources = this.sources.filter(s => s.status === 'نشط');
        const sourcesWithNews = this.sources.filter(s => 
            this.news.some(n => n.source === s.name)
        );

        document.getElementById('totalSources').textContent = this.sources.length;
        document.getElementById('activeSources').textContent = activeSources.length;
        document.getElementById('sourcesWithNews').textContent = sourcesWithNews.length;
    }

    displaySources(sourcesToShow = this.sources) {
        const container = document.getElementById('sourcesContainer');
        
        if (sourcesToShow.length === 0) {
            container.innerHTML = `
                <div class="no-sources">
                    <i class="fas fa-building"></i>
                    <p>لا توجد مصادر تطابق معايير البحث</p>
                </div>
            `;
            return;
        }

        container.innerHTML = sourcesToShow.map(source => this.createSourceCard(source)).join('');
    }

    createSourceCard(source) {
        const newsCount = this.news.filter(n => n.source === source.name).length;
        const statusClass = source.status === 'نشط' ? 'status-active' : 'status-inactive';
        
        const websiteHtml = source.website 
            ? `<a href="${source.website}" target="_blank" class="btn btn-secondary btn-sm">
                <i class="fas fa-external-link-alt"></i> زيارة الموقع
               </a>`
            : '';

        return `
            <div class="source-item">
                <div class="source-header">
                    <div class="source-info">
                        <h3 class="source-name">${source.name}</h3>
                        <div class="source-meta">
                            <span class="source-type"><i class="fas fa-tag"></i> ${source.type}</span>
                            ${source.country ? `<span class="source-country"><i class="fas fa-globe"></i> ${source.country}</span>` : ''}
                            <span class="source-language"><i class="fas fa-language"></i> ${source.language}</span>
                            <span class="source-status ${statusClass}">
                                <i class="fas ${source.status === 'نشط' ? 'fa-check-circle' : 'fa-times-circle'}"></i> 
                                ${source.status}
                            </span>
                        </div>
                    </div>
                    <div class="source-stats">
                        <div class="news-count">
                            <i class="fas fa-newspaper"></i>
                            <span>${newsCount} خبر</span>
                        </div>
                    </div>
                </div>
                
                ${source.description ? `<div class="source-description">${source.description}</div>` : ''}
                
                ${source.contact ? `
                    <div class="source-contact">
                        <i class="fas fa-phone"></i> ${source.contact}
                    </div>
                ` : ''}
                
                ${source.notes ? `
                    <div class="source-notes">
                        <i class="fas fa-sticky-note"></i> ${source.notes}
                    </div>
                ` : ''}
                
                <div class="source-actions">
                    ${websiteHtml}
                    <button onclick="sourcesManager.openModal(${source.id})" class="btn btn-secondary btn-sm">
                        <i class="fas fa-edit"></i> تحرير
                    </button>
                    <button onclick="sourcesManager.deleteSource(${source.id})" class="btn btn-danger btn-sm">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
    }

    filterSources() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('typeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        const filtered = this.sources.filter(source => {
            const matchesSearch = !searchTerm || 
                source.name.toLowerCase().includes(searchTerm) ||
                (source.description && source.description.toLowerCase().includes(searchTerm)) ||
                (source.country && source.country.toLowerCase().includes(searchTerm));
            
            const matchesType = !typeFilter || source.type === typeFilter;
            const matchesStatus = !statusFilter || source.status === statusFilter;

            return matchesSearch && matchesType && matchesStatus;
        });

        this.displaySources(filtered);
    }

    clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('typeFilter').value = '';
        document.getElementById('statusFilter').value = '';
        this.displaySources();
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        const bgColor = type === 'error' ? '#dc3545' : '#4CAF50';
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            max-width: 300px;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 4000);
    }
}

// تشغيل التطبيق
const sourcesManager = new SourcesManager();
