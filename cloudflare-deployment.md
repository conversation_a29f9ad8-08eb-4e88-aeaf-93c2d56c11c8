# 🌐 رفع موقع الرصد الإعلامي على Cloudflare Pages

## 📁 الملفات المطلوبة للرفع:

### ملفات الموقع الأساسية:
- ✅ index.html
- ✅ sources.html  
- ✅ style.css
- ✅ script.js
- ✅ sources.js
- ✅ README.md

### ملفات الخادم (لا تُرفع على Cloudflare Pages):
- ❌ server.js
- ❌ scraper.js
- ❌ package.json
- ❌ node_modules/

## 🔧 تعديلات مطلوبة قبل الرفع:

### 1. إنشاء مجلد منفصل للموقع:
```
website/
├── index.html
├── sources.html
├── style.css
├── script.js
├── sources.js
└── README.md
```

### 2. تعديل script.js لإزالة الزحف التلقائي:
نحتاج لإزالة أو تعطيل دالة `fetchNewsAutomatically()` لأن Cloudflare Pages لا يدعم Node.js

### 3. إضافة ملف _redirects (اختياري):
لإعادة توجيه الصفحات بشكل صحيح

## 📋 خطوات الرفع:

### الطريقة الأولى: رفع مباشر (الأسهل)

1. **إنشاء مجلد جديد** على سطح المكتب اسمه `news-website`

2. **نسخ الملفات التالية فقط:**
   - index.html
   - sources.html
   - style.css
   - script.js (بعد التعديل)
   - sources.js
   - README.md

3. **ضغط المجلد** في ملف ZIP

4. **في Cloudflare Pages:**
   - اضغط "select from computer"
   - اختر ملف ZIP
   - اضغط "Deploy site"

### الطريقة الثانية: ربط مع GitHub (الأفضل)

1. **إنشاء repository جديد على GitHub**
2. **رفع الملفات المطلوبة فقط**
3. **في Cloudflare Pages:**
   - اختر "Connect to Git"
   - اختر GitHub repository
   - اضغط "Begin setup"

## ⚠️ تحديات الزحف التلقائي:

### المشكلة:
Cloudflare Pages لا يدعم Node.js أو الخوادم، لذا الزحف التلقائي لن يعمل.

### الحلول البديلة:

#### الحل 1: استخدام Cloudflare Workers
```javascript
// worker.js - يعمل على Cloudflare Workers
export default {
  async fetch(request, env, ctx) {
    // كود الزحف هنا
  }
}
```

#### الحل 2: استخدام خدمة منفصلة
- رفع الخادم على Heroku أو Railway
- ربطه مع الموقع على Cloudflare

#### الحل 3: API خارجي
- استخدام NewsAPI أو خدمة مشابهة
- دفع رسوم شهرية بسيطة

## 🎯 التوصية:

### للبداية (مجاني 100%):
1. ارفع الموقع بدون الزحف التلقائي
2. استخدم الإدخال اليدوي للأخبار
3. استخدم المصادر المحفوظة مسبقاً

### للتطوير المستقبلي:
1. ارفع الخادم على Heroku (مجاني محدود)
2. أو استخدم Cloudflare Workers (مجاني محدود)
3. أو ادفع لخدمة API أخبار

## 📝 ملف التعليمات للموقع المرفوع:

سأنشئ ملف README.md محدث للموقع المرفوع يوضح:
- كيفية الاستخدام بدون خادم
- كيفية إضافة الأخبار يدوياً
- كيفية استخدام المصادر المحفوظة

هل تريد المتابعة مع هذا الحل؟
