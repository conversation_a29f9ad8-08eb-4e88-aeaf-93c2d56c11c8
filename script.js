// تطبيق الرصد الإعلامي
class NewsMonitor {
    constructor() {
        this.news = JSON.parse(localStorage.getItem('newsData')) || [];
        this.currentEditId = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateStats();
        this.populateFilters();
        this.displayNews();
        this.setDefaultDateTime();
    }

    bindEvents() {
        // أحداث الأزرار والنماذج
        document.getElementById('addNewsBtn').addEventListener('click', () => this.openModal());
        document.getElementById('newsForm').addEventListener('submit', (e) => this.handleSubmit(e));
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        document.querySelector('.close').addEventListener('click', () => this.closeModal());
        
        // أحداث البحث والفلترة
        document.getElementById('searchInput').addEventListener('input', () => this.filterNews());
        document.getElementById('categoryFilter').addEventListener('change', () => this.filterNews());
        document.getElementById('sourceFilter').addEventListener('change', () => this.filterNews());
        document.getElementById('dateFilter').addEventListener('change', () => this.filterNews());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearFilters());

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('newsModal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    setDefaultDateTime() {
        const now = new Date();
        const date = now.toISOString().split('T')[0];
        const time = now.toTimeString().split(' ')[0].substring(0, 5);
        
        document.getElementById('newsDate').value = date;
        document.getElementById('newsTime').value = time;
    }

    openModal(newsId = null) {
        const modal = document.getElementById('newsModal');
        const modalTitle = document.getElementById('modalTitle');
        const form = document.getElementById('newsForm');
        
        if (newsId) {
            // تحرير خبر موجود
            const news = this.news.find(n => n.id === newsId);
            if (news) {
                modalTitle.textContent = 'تحرير الخبر';
                this.currentEditId = newsId;
                this.fillForm(news);
            }
        } else {
            // إضافة خبر جديد
            modalTitle.textContent = 'إضافة خبر جديد';
            this.currentEditId = null;
            form.reset();
            this.setDefaultDateTime();
        }
        
        modal.style.display = 'block';
    }

    closeModal() {
        document.getElementById('newsModal').style.display = 'none';
        document.getElementById('newsForm').reset();
        this.currentEditId = null;
    }

    fillForm(news) {
        document.getElementById('newsTitle').value = news.title;
        document.getElementById('newsContent').value = news.content;
        document.getElementById('newsSource').value = news.source;
        document.getElementById('newsCategory').value = news.category;
        document.getElementById('newsDate').value = news.date;
        document.getElementById('newsTime').value = news.time;
        document.getElementById('newsUrl').value = news.url || '';
        document.getElementById('newsTags').value = news.tags ? news.tags.join(', ') : '';
    }

    handleSubmit(e) {
        e.preventDefault();
        
        const formData = {
            title: document.getElementById('newsTitle').value.trim(),
            content: document.getElementById('newsContent').value.trim(),
            source: document.getElementById('newsSource').value.trim(),
            category: document.getElementById('newsCategory').value.trim(),
            date: document.getElementById('newsDate').value,
            time: document.getElementById('newsTime').value,
            url: document.getElementById('newsUrl').value.trim(),
            tags: document.getElementById('newsTags').value.split(',').map(tag => tag.trim()).filter(tag => tag)
        };

        if (this.currentEditId) {
            // تحديث خبر موجود
            const index = this.news.findIndex(n => n.id === this.currentEditId);
            if (index !== -1) {
                this.news[index] = { ...this.news[index], ...formData };
            }
        } else {
            // إضافة خبر جديد
            const newNews = {
                id: Date.now(),
                ...formData,
                createdAt: new Date().toISOString()
            };
            this.news.unshift(newNews);
        }

        this.saveData();
        this.updateStats();
        this.populateFilters();
        this.displayNews();
        this.closeModal();
        
        this.showNotification(this.currentEditId ? 'تم تحديث الخبر بنجاح' : 'تم إضافة الخبر بنجاح');
    }

    deleteNews(id) {
        if (confirm('هل أنت متأكد من حذف هذا الخبر؟')) {
            this.news = this.news.filter(n => n.id !== id);
            this.saveData();
            this.updateStats();
            this.populateFilters();
            this.displayNews();
            this.showNotification('تم حذف الخبر بنجاح');
        }
    }

    saveData() {
        localStorage.setItem('newsData', JSON.stringify(this.news));
    }

    updateStats() {
        const today = new Date().toISOString().split('T')[0];
        const todayNews = this.news.filter(n => n.date === today);
        const categories = [...new Set(this.news.map(n => n.category))];

        document.getElementById('totalNews').textContent = this.news.length;
        document.getElementById('todayNews').textContent = todayNews.length;
        document.getElementById('totalCategories').textContent = categories.length;
    }

    populateFilters() {
        const categories = [...new Set(this.news.map(n => n.category))];
        const sources = [...new Set(this.news.map(n => n.source))];

        this.populateSelect('categoryFilter', categories);
        this.populateSelect('sourceFilter', sources);
    }

    populateSelect(selectId, options) {
        const select = document.getElementById(selectId);
        const currentValue = select.value;
        
        // الاحتفاظ بالخيار الأول (جميع...)
        const firstOption = select.children[0];
        select.innerHTML = '';
        select.appendChild(firstOption);

        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            select.appendChild(optionElement);
        });

        select.value = currentValue;
    }

    displayNews(newsToShow = this.news) {
        const container = document.getElementById('newsContainer');
        
        if (newsToShow.length === 0) {
            container.innerHTML = `
                <div class="no-news">
                    <i class="fas fa-newspaper"></i>
                    <p>لا توجد أخبار تطابق معايير البحث</p>
                </div>
            `;
            return;
        }

        container.innerHTML = newsToShow.map(news => this.createNewsCard(news)).join('');
    }

    createNewsCard(news) {
        const tagsHtml = news.tags && news.tags.length > 0 
            ? `<div class="news-tags">${news.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>`
            : '';

        const urlHtml = news.url 
            ? `<a href="${news.url}" target="_blank" class="btn btn-secondary"><i class="fas fa-external-link-alt"></i> رابط الخبر</a>`
            : '';

        return `
            <div class="news-item">
                <div class="news-header">
                    <div>
                        <h3 class="news-title">${news.title}</h3>
                        <div class="news-meta">
                            <span><i class="fas fa-calendar"></i> ${news.date}</span>
                            <span><i class="fas fa-clock"></i> ${news.time}</span>
                            <span><i class="fas fa-newspaper"></i> ${news.source}</span>
                            <span><i class="fas fa-tag"></i> ${news.category}</span>
                        </div>
                    </div>
                </div>
                <div class="news-content">${news.content}</div>
                ${tagsHtml}
                <div class="news-actions">
                    ${urlHtml}
                    <button onclick="newsMonitor.openModal(${news.id})" class="btn btn-secondary">
                        <i class="fas fa-edit"></i> تحرير
                    </button>
                    <button onclick="newsMonitor.deleteNews(${news.id})" class="btn btn-danger">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
    }

    filterNews() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const sourceFilter = document.getElementById('sourceFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;

        const filtered = this.news.filter(news => {
            const matchesSearch = !searchTerm || 
                news.title.toLowerCase().includes(searchTerm) ||
                news.content.toLowerCase().includes(searchTerm) ||
                (news.tags && news.tags.some(tag => tag.toLowerCase().includes(searchTerm)));
            
            const matchesCategory = !categoryFilter || news.category === categoryFilter;
            const matchesSource = !sourceFilter || news.source === sourceFilter;
            const matchesDate = !dateFilter || news.date === dateFilter;

            return matchesSearch && matchesCategory && matchesSource && matchesDate;
        });

        this.displayNews(filtered);
    }

    clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('sourceFilter').value = '';
        document.getElementById('dateFilter').value = '';
        this.displayNews();
    }

    showNotification(message) {
        // إنشاء إشعار بسيط
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            document.body.removeChild(notification);
        }, 3000);
    }
}

// تشغيل التطبيق
const newsMonitor = new NewsMonitor();
