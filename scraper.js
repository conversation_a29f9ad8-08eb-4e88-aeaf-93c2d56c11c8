const puppeteer = require('puppeteer');
const cheerio = require('cheerio');

class IraqiNewsScraper {
    constructor() {
        this.sources = [
            {
                name: 'شفق نيوز',
                url: 'https://shafaq.com/ar',
                type: 'puppeteer',
                selectors: {
                    articles: '.news-item, .article-item',
                    title: 'h2, h3, .title',
                    content: '.summary, .excerpt, .content',
                    link: 'a',
                    image: 'img'
                }
            },
            {
                name: 'السومرية نيوز',
                url: 'https://www.alsumaria.tv/',
                type: 'puppeteer',
                selectors: {
                    articles: '.article, .news-item',
                    title: 'h2, h3, .headline',
                    content: '.summary, .excerpt',
                    link: 'a',
                    image: 'img'
                }
            },
            {
                name: 'بغداد اليوم',
                url: 'https://baghdadtoday.news/lastnews',
                type: 'puppeteer',
                selectors: {
                    articles: '.news-item, .article',
                    title: 'h2, h3, .title',
                    content: '.content, .summary',
                    link: 'a',
                    image: 'img'
                }
            },
            {
                name: 'وكالة المعلومة',
                url: 'https://www.almaalomah.me/',
                type: 'puppeteer',
                selectors: {
                    articles: '.news-item, .article',
                    title: 'h2, h3, .title',
                    content: '.summary, .excerpt',
                    link: 'a',
                    image: 'img'
                }
            }
        ];
    }

    async scrapeWithPuppeteer(source) {
        let browser;
        try {
            console.log(`🕷️ بدء زحف ${source.name}...`);
            
            browser = await puppeteer.launch({ 
                headless: 'new',
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });
            
            const page = await browser.newPage();
            
            // تعيين User Agent عربي
            await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
            
            // تعيين اللغة العربية
            await page.setExtraHTTPHeaders({
                'Accept-Language': 'ar,en;q=0.9'
            });
            
            await page.goto(source.url, { 
                waitUntil: 'networkidle2',
                timeout: 30000 
            });
            
            // انتظار تحميل المحتوى
            await page.waitForTimeout(3000);
            
            const articles = await page.evaluate((selectors, sourceName) => {
                const results = [];
                
                // البحث عن المقالات بطرق متعددة
                const articleSelectors = [
                    selectors.articles,
                    '.news-item',
                    '.article-item',
                    '.post-item',
                    '.entry',
                    '[class*="news"]',
                    '[class*="article"]'
                ];
                
                let articleElements = [];
                for (const selector of articleSelectors) {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        articleElements = elements;
                        break;
                    }
                }
                
                // إذا لم نجد مقالات، نبحث عن العناوين مباشرة
                if (articleElements.length === 0) {
                    const titleElements = document.querySelectorAll('h1, h2, h3, h4, .title, .headline');
                    titleElements.forEach((titleEl, index) => {
                        if (index < 10) { // أول 10 عناوين
                            const title = titleEl.innerText?.trim();
                            if (title && title.length > 10) {
                                const link = titleEl.closest('a')?.href || 
                                           titleEl.querySelector('a')?.href || 
                                           window.location.href;
                                
                                results.push({
                                    title,
                                    content: title, // استخدام العنوان كمحتوى مؤقت
                                    url: link,
                                    source: sourceName,
                                    date: new Date().toISOString().split('T')[0],
                                    time: new Date().toTimeString().split(' ')[0].substring(0, 5),
                                    category: 'أخبار عامة',
                                    tags: []
                                });
                            }
                        }
                    });
                } else {
                    // معالجة المقالات العادية
                    articleElements.forEach((article, index) => {
                        if (index < 15) { // أول 15 مقال
                            try {
                                const titleSelectors = [selectors.title, 'h1', 'h2', 'h3', '.title', '.headline'];
                                const contentSelectors = [selectors.content, '.summary', '.excerpt', '.content', 'p'];
                                
                                let title = '';
                                let content = '';
                                let link = '';
                                
                                // البحث عن العنوان
                                for (const titleSel of titleSelectors) {
                                    const titleEl = article.querySelector(titleSel);
                                    if (titleEl && titleEl.innerText?.trim()) {
                                        title = titleEl.innerText.trim();
                                        break;
                                    }
                                }
                                
                                // البحث عن المحتوى
                                for (const contentSel of contentSelectors) {
                                    const contentEl = article.querySelector(contentSel);
                                    if (contentEl && contentEl.innerText?.trim()) {
                                        content = contentEl.innerText.trim();
                                        break;
                                    }
                                }
                                
                                // البحث عن الرابط
                                const linkEl = article.querySelector('a') || article.closest('a');
                                if (linkEl && linkEl.href) {
                                    link = linkEl.href;
                                }
                                
                                // التأكد من وجود عنوان على الأقل
                                if (title && title.length > 10) {
                                    results.push({
                                        title,
                                        content: content || title,
                                        url: link || window.location.href,
                                        source: sourceName,
                                        date: new Date().toISOString().split('T')[0],
                                        time: new Date().toTimeString().split(' ')[0].substring(0, 5),
                                        category: 'أخبار عامة',
                                        tags: []
                                    });
                                }
                            } catch (error) {
                                console.log('خطأ في معالجة مقال:', error);
                            }
                        }
                    });
                }
                
                return results;
            }, source.selectors, source.name);
            
            console.log(`✅ تم جلب ${articles.length} خبر من ${source.name}`);
            return articles;
            
        } catch (error) {
            console.error(`❌ خطأ في زحف ${source.name}:`, error.message);
            return [];
        } finally {
            if (browser) {
                await browser.close();
            }
        }
    }

    async scrapeAll() {
        console.log('🚀 بدء عملية الزحف الشاملة...');
        const allNews = [];
        
        for (const source of this.sources) {
            try {
                const news = await this.scrapeWithPuppeteer(source);
                allNews.push(...news);
                
                // انتظار 3 ثوان بين كل موقع لتجنب الحظر
                console.log('⏳ انتظار 3 ثوان...');
                await new Promise(resolve => setTimeout(resolve, 3000));
                
            } catch (error) {
                console.error(`خطأ في معالجة ${source.name}:`, error);
            }
        }
        
        // إزالة المكررات
        const uniqueNews = this.removeDuplicates(allNews);
        
        console.log(`🎉 انتهت عملية الزحف. تم جلب ${uniqueNews.length} خبر فريد`);
        return uniqueNews;
    }

    removeDuplicates(news) {
        const seen = new Set();
        return news.filter(item => {
            const key = item.title.toLowerCase().trim();
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    async scrapeSource(sourceName) {
        const source = this.sources.find(s => s.name === sourceName);
        if (!source) {
            throw new Error(`المصدر ${sourceName} غير موجود`);
        }
        
        return await this.scrapeWithPuppeteer(source);
    }
}

module.exports = IraqiNewsScraper;
