# نظام الزحف التلقائي للأخبار العراقية

## المتطلبات:
1. Node.js (تحميل من nodejs.org)
2. مساحة استضافة بسيطة أو تشغيل محلي

## خطوات التثبيت:

### 1. إنشاء مجلد المشروع:
```bash
mkdir news-scraper
cd news-scraper
npm init -y
```

### 2. تثبيت المكتبات المطلوبة:
```bash
npm install puppeteer express cors cheerio axios
```

### 3. إنشاء ملف الزحف (scraper.js):
```javascript
const puppeteer = require('puppeteer');
const cheerio = require('cheerio');
const axios = require('axios');

class NewsScraper {
    constructor() {
        this.sources = [
            {
                name: 'شفق نيوز',
                url: 'https://shafaq.com/ar',
                selectors: {
                    articles: '.news-item',
                    title: '.title',
                    content: '.content',
                    link: 'a'
                }
            },
            {
                name: 'السومرية نيوز',
                url: 'https://www.alsumaria.tv/',
                selectors: {
                    articles: '.article',
                    title: 'h2',
                    content: '.summary',
                    link: 'a'
                }
            }
            // يمكن إضافة المزيد من المصادر
        ];
    }

    async scrapeSource(source) {
        try {
            const browser = await puppeteer.launch({ headless: true });
            const page = await browser.newPage();
            
            await page.goto(source.url, { waitUntil: 'networkidle2' });
            
            const articles = await page.evaluate((selectors) => {
                const articleElements = document.querySelectorAll(selectors.articles);
                const results = [];
                
                articleElements.forEach(article => {
                    const title = article.querySelector(selectors.title)?.innerText?.trim();
                    const content = article.querySelector(selectors.content)?.innerText?.trim();
                    const link = article.querySelector(selectors.link)?.href;
                    
                    if (title && content) {
                        results.push({
                            title,
                            content: content.substring(0, 500), // أول 500 حرف
                            url: link,
                            source: source.name,
                            date: new Date().toISOString().split('T')[0],
                            time: new Date().toTimeString().split(' ')[0].substring(0, 5)
                        });
                    }
                });
                
                return results;
            }, source.selectors);
            
            await browser.close();
            return articles;
            
        } catch (error) {
            console.error(`خطأ في زحف ${source.name}:`, error);
            return [];
        }
    }

    async scrapeAll() {
        const allNews = [];
        
        for (const source of this.sources) {
            console.log(`جاري زحف ${source.name}...`);
            const news = await this.scrapeSource(source);
            allNews.push(...news);
            
            // انتظار ثانيتين بين كل موقع لتجنب الحظر
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        return allNews;
    }
}

module.exports = NewsScraper;
```

### 4. إنشاء خادم API (server.js):
```javascript
const express = require('express');
const cors = require('cors');
const NewsScraper = require('./scraper');

const app = express();
const port = 3000;

app.use(cors());
app.use(express.json());

const scraper = new NewsScraper();

// API لجلب الأخبار
app.get('/api/scrape-news', async (req, res) => {
    try {
        console.log('بدء عملية الزحف...');
        const news = await scraper.scrapeAll();
        
        res.json({
            success: true,
            count: news.length,
            news: news
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// API لجلب أخبار مصدر واحد
app.get('/api/scrape-source/:sourceName', async (req, res) => {
    try {
        const sourceName = req.params.sourceName;
        const source = scraper.sources.find(s => s.name === sourceName);
        
        if (!source) {
            return res.status(404).json({
                success: false,
                error: 'المصدر غير موجود'
            });
        }
        
        const news = await scraper.scrapeSource(source);
        
        res.json({
            success: true,
            source: sourceName,
            count: news.length,
            news: news
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.listen(port, () => {
    console.log(`خادم الزحف يعمل على http://localhost:${port}`);
});
```

### 5. تشغيل النظام:
```bash
node server.js
```

## الاستخدام مع موقعك:

### إضافة زر الجلب التلقائي في index.html:
```html
<button id="autoFetchBtn" class="btn btn-success">
    <i class="fas fa-download"></i> جلب الأخبار تلقائياً
</button>
```

### إضافة الكود في script.js:
```javascript
async function fetchNewsAutomatically() {
    try {
        showLoading(true);
        
        const response = await fetch('http://localhost:3000/api/scrape-news');
        const data = await response.json();
        
        if (data.success) {
            // إضافة الأخبار المجلبة
            data.news.forEach(newsItem => {
                const newNews = {
                    id: Date.now() + Math.random(),
                    ...newsItem,
                    category: 'أخبار عامة', // يمكن تحسينها
                    tags: [],
                    createdAt: new Date().toISOString()
                };
                
                this.news.unshift(newNews);
            });
            
            this.saveData();
            this.updateStats();
            this.displayNews();
            
            this.showNotification(`تم جلب ${data.count} خبر بنجاح`);
        }
    } catch (error) {
        this.showNotification('فشل في جلب الأخبار', 'error');
    } finally {
        showLoading(false);
    }
}

// ربط الزر
document.getElementById('autoFetchBtn').addEventListener('click', () => {
    fetchNewsAutomatically();
});
```

## المميزات:
- ✅ زحف تلقائي من مصادر متعددة
- ✅ تجنب الحظر بفواصل زمنية
- ✅ استخراج العنوان والمحتوى والرابط
- ✅ حفظ تلقائي في قاعدة البيانات المحلية
- ✅ إمكانية إضافة مصادر جديدة بسهولة

## ملاحظات مهمة:
1. **القانونية**: تأكد من احترام شروط الاستخدام للمواقع
2. **الأخلاقية**: لا تفرط في الطلبات لتجنب إرهاق الخوادم
3. **التحديث**: قد تحتاج لتحديث selectors عند تغيير تصميم المواقع
4. **الاستضافة**: يمكن رفعه على Heroku أو Vercel مجاناً

هل تريد مني إنشاء هذا النظام كاملاً؟
