const express = require('express');
const cors = require('cors');
const cron = require('node-cron');
const IraqiNewsScraper = require('./scraper');

const app = express();
const port = process.env.PORT || 3000;

// إعداد CORS للسماح بالوصول من الموقع
app.use(cors({
    origin: ['http://localhost', 'http://127.0.0.1', 'file://'],
    credentials: true
}));

app.use(express.json());
app.use(express.static('public'));

const scraper = new IraqiNewsScraper();
let lastScrapeTime = null;
let cachedNews = [];

// API الرئيسي لجلب جميع الأخبار
app.get('/api/scrape-news', async (req, res) => {
    try {
        console.log('📡 طلب جلب الأخبار...');
        
        const news = await scraper.scrapeAll();
        
        cachedNews = news;
        lastScrapeTime = new Date();
        
        res.json({
            success: true,
            count: news.length,
            news: news,
            scrapeTime: lastScrapeTime,
            message: `تم جلب ${news.length} خبر بنجاح`
        });
        
    } catch (error) {
        console.error('خطأ في API:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'فشل في جلب الأخبار'
        });
    }
});

// API لجلب أخبار مصدر واحد
app.get('/api/scrape-source/:sourceName', async (req, res) => {
    try {
        const sourceName = decodeURIComponent(req.params.sourceName);
        console.log(`📡 طلب جلب أخبار ${sourceName}...`);
        
        const news = await scraper.scrapeSource(sourceName);
        
        res.json({
            success: true,
            source: sourceName,
            count: news.length,
            news: news,
            scrapeTime: new Date(),
            message: `تم جلب ${news.length} خبر من ${sourceName}`
        });
        
    } catch (error) {
        console.error('خطأ في API:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            message: `فشل في جلب أخبار ${req.params.sourceName}`
        });
    }
});

// API للحصول على قائمة المصادر المتاحة
app.get('/api/sources', (req, res) => {
    const sources = scraper.sources.map(source => ({
        name: source.name,
        url: source.url,
        type: source.type
    }));
    
    res.json({
        success: true,
        sources: sources,
        count: sources.length
    });
});

// API للحصول على الأخبار المحفوظة مؤقتاً
app.get('/api/cached-news', (req, res) => {
    res.json({
        success: true,
        count: cachedNews.length,
        news: cachedNews,
        lastScrapeTime: lastScrapeTime,
        message: cachedNews.length > 0 ? 'أخبار محفوظة مؤقتاً' : 'لا توجد أخبار محفوظة'
    });
});

// API لاختبار الاتصال
app.get('/api/test', (req, res) => {
    res.json({
        success: true,
        message: 'خادم الزحف يعمل بشكل طبيعي',
        timestamp: new Date(),
        availableSources: scraper.sources.length
    });
});

// صفحة رئيسية بسيطة
app.get('/', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>خادم زحف الأخبار العراقية</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
                .status { padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0; }
                .endpoint { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .method { background: #007bff; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; }
                h1 { color: #333; text-align: center; }
                h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
                code { background: #f1f1f1; padding: 2px 5px; border-radius: 3px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🕷️ خادم زحف الأخبار العراقية</h1>
                
                <div class="status">
                    ✅ الخادم يعمل بشكل طبيعي<br>
                    🕐 الوقت: ${new Date().toLocaleString('ar-EG')}<br>
                    📊 المصادر المتاحة: ${scraper.sources.length}<br>
                    📰 آخر عملية زحف: ${lastScrapeTime ? lastScrapeTime.toLocaleString('ar-EG') : 'لم يتم بعد'}
                </div>

                <h2>📡 نقاط النهاية المتاحة (APIs)</h2>
                
                <div class="endpoint">
                    <span class="method">GET</span>
                    <code>/api/test</code> - اختبار الاتصال
                </div>
                
                <div class="endpoint">
                    <span class="method">GET</span>
                    <code>/api/scrape-news</code> - جلب جميع الأخبار
                </div>
                
                <div class="endpoint">
                    <span class="method">GET</span>
                    <code>/api/scrape-source/[اسم المصدر]</code> - جلب أخبار مصدر واحد
                </div>
                
                <div class="endpoint">
                    <span class="method">GET</span>
                    <code>/api/sources</code> - قائمة المصادر المتاحة
                </div>
                
                <div class="endpoint">
                    <span class="method">GET</span>
                    <code>/api/cached-news</code> - الأخبار المحفوظة مؤقتاً
                </div>

                <h2>🎯 المصادر المتاحة</h2>
                ${scraper.sources.map(source => `
                    <div class="endpoint">
                        📰 <strong>${source.name}</strong><br>
                        🔗 <a href="${source.url}" target="_blank">${source.url}</a>
                    </div>
                `).join('')}

                <h2>📝 كيفية الاستخدام</h2>
                <p>لاستخدام هذا الخادم مع موقع الرصد الإعلامي:</p>
                <ol>
                    <li>تأكد من تشغيل هذا الخادم</li>
                    <li>في موقع الرصد الإعلامي، اضغط على زر "جلب الأخبار تلقائياً"</li>
                    <li>سيتم جلب الأخبار من جميع المصادر تلقائياً</li>
                </ol>
            </div>
        </body>
        </html>
    `);
});

// جدولة تلقائية لجلب الأخبار كل ساعة
cron.schedule('0 * * * *', async () => {
    console.log('⏰ جدولة تلقائية: بدء جلب الأخبار...');
    try {
        const news = await scraper.scrapeAll();
        cachedNews = news;
        lastScrapeTime = new Date();
        console.log(`✅ جدولة تلقائية: تم جلب ${news.length} خبر`);
    } catch (error) {
        console.error('❌ خطأ في الجدولة التلقائية:', error);
    }
});

// معالجة الأخطاء
app.use((error, req, res, next) => {
    console.error('خطأ في الخادم:', error);
    res.status(500).json({
        success: false,
        error: 'خطأ داخلي في الخادم',
        message: 'حدث خطأ غير متوقع'
    });
});

// بدء الخادم
app.listen(port, () => {
    console.log(`🚀 خادم زحف الأخبار العراقية يعمل على:`);
    console.log(`   http://localhost:${port}`);
    console.log(`📡 APIs متاحة على:`);
    console.log(`   http://localhost:${port}/api/scrape-news`);
    console.log(`   http://localhost:${port}/api/sources`);
    console.log(`🕷️ جاهز لزحف ${scraper.sources.length} مصدر إخباري`);
});

module.exports = app;
