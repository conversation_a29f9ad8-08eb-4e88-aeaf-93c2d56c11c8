// تطبيق الرصد الإعلامي - نسخة Cloudflare Pages
class NewsMonitor {
    constructor() {
        this.news = JSON.parse(localStorage.getItem('newsData')) || [];
        this.sources = JSON.parse(localStorage.getItem('sourcesData')) || [];
        this.currentEditId = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateStats();
        this.populateFilters();
        this.populateSourcesDropdown();
        this.displayNews();
        this.setDefaultDateTime();
    }

    bindEvents() {
        // أحداث الأزرار والنماذج
        document.getElementById('addNewsBtn').addEventListener('click', () => this.openModal());
        document.getElementById('newsForm').addEventListener('submit', (e) => this.handleSubmit(e));
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        document.querySelector('.close').addEventListener('click', () => this.closeModal());
        
        // أحداث البحث والفلترة
        document.getElementById('searchInput').addEventListener('input', () => this.filterNews());
        document.getElementById('categoryFilter').addEventListener('change', () => this.filterNews());
        document.getElementById('sourceFilter').addEventListener('change', () => this.filterNews());
        document.getElementById('dateFilter').addEventListener('change', () => this.filterNews());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearFilters());
        
        // أحداث قائمة المصادر
        document.getElementById('newsSourceSelect').addEventListener('change', (e) => {
            if (e.target.value) {
                document.getElementById('newsSource').value = e.target.value;
            }
        });

        // زر المساعدة للزحف (بدلاً من الزحف التلقائي)
        const helpBtn = document.getElementById('autoFetchBtn');
        if (helpBtn) {
            helpBtn.addEventListener('click', () => this.showScrapingHelp());
        }

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('newsModal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    setDefaultDateTime() {
        const now = new Date();
        const date = now.toISOString().split('T')[0];
        const time = now.toTimeString().split(' ')[0].substring(0, 5);
        
        document.getElementById('newsDate').value = date;
        document.getElementById('newsTime').value = time;
    }

    openModal(newsId = null) {
        const modal = document.getElementById('newsModal');
        const modalTitle = document.getElementById('modalTitle');
        const form = document.getElementById('newsForm');
        
        // تحديث قائمة المصادر
        this.populateSourcesDropdown();
        
        if (newsId) {
            // تحرير خبر موجود
            const news = this.news.find(n => n.id === newsId);
            if (news) {
                modalTitle.textContent = 'تحرير الخبر';
                this.currentEditId = newsId;
                this.fillForm(news);
            }
        } else {
            // إضافة خبر جديد
            modalTitle.textContent = 'إضافة خبر جديد';
            this.currentEditId = null;
            form.reset();
            this.setDefaultDateTime();
        }
        
        modal.style.display = 'block';
    }

    closeModal() {
        document.getElementById('newsModal').style.display = 'none';
        document.getElementById('newsForm').reset();
        this.currentEditId = null;
    }

    fillForm(news) {
        document.getElementById('newsTitle').value = news.title;
        document.getElementById('newsContent').value = news.content;
        document.getElementById('newsSource').value = news.source;
        document.getElementById('newsCategory').value = news.category;
        document.getElementById('newsDate').value = news.date;
        document.getElementById('newsTime').value = news.time;
        document.getElementById('newsUrl').value = news.url || '';
        document.getElementById('newsTags').value = news.tags ? news.tags.join(', ') : '';
    }

    handleSubmit(e) {
        e.preventDefault();
        
        const formData = {
            title: document.getElementById('newsTitle').value.trim(),
            content: document.getElementById('newsContent').value.trim(),
            source: document.getElementById('newsSource').value.trim(),
            category: document.getElementById('newsCategory').value.trim(),
            date: document.getElementById('newsDate').value,
            time: document.getElementById('newsTime').value,
            url: document.getElementById('newsUrl').value.trim(),
            tags: document.getElementById('newsTags').value.split(',').map(tag => tag.trim()).filter(tag => tag)
        };

        if (this.currentEditId) {
            // تحديث خبر موجود
            const index = this.news.findIndex(n => n.id === this.currentEditId);
            if (index !== -1) {
                this.news[index] = { ...this.news[index], ...formData };
            }
        } else {
            // إضافة خبر جديد
            const newNews = {
                id: Date.now(),
                ...formData,
                createdAt: new Date().toISOString()
            };
            this.news.unshift(newNews);
        }

        this.saveData();
        this.updateStats();
        this.populateFilters();
        this.displayNews();
        this.closeModal();
        
        this.showNotification(this.currentEditId ? 'تم تحديث الخبر بنجاح' : 'تم إضافة الخبر بنجاح');
    }

    deleteNews(id) {
        if (confirm('هل أنت متأكد من حذف هذا الخبر؟')) {
            this.news = this.news.filter(n => n.id !== id);
            this.saveData();
            this.updateStats();
            this.populateFilters();
            this.displayNews();
            this.showNotification('تم حذف الخبر بنجاح');
        }
    }

    saveData() {
        localStorage.setItem('newsData', JSON.stringify(this.news));
    }

    updateStats() {
        const today = new Date().toISOString().split('T')[0];
        const todayNews = this.news.filter(n => n.date === today);
        const categories = [...new Set(this.news.map(n => n.category))];

        document.getElementById('totalNews').textContent = this.news.length;
        document.getElementById('todayNews').textContent = todayNews.length;
        document.getElementById('totalCategories').textContent = categories.length;
    }

    populateFilters() {
        const categories = [...new Set(this.news.map(n => n.category))];
        const sources = [...new Set(this.news.map(n => n.source))];

        this.populateSelect('categoryFilter', categories);
        this.populateSelect('sourceFilter', sources);
    }

    populateSelect(selectId, options) {
        const select = document.getElementById(selectId);
        const currentValue = select.value;
        
        // الاحتفاظ بالخيار الأول (جميع...)
        const firstOption = select.children[0];
        select.innerHTML = '';
        select.appendChild(firstOption);

        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            select.appendChild(optionElement);
        });

        select.value = currentValue;
    }

    populateSourcesDropdown() {
        const select = document.getElementById('newsSourceSelect');
        if (!select) return;
        
        // الاحتفاظ بالخيار الأول
        const firstOption = select.children[0];
        select.innerHTML = '';
        select.appendChild(firstOption);

        // إضافة المصادر النشطة فقط
        const activeSources = this.sources.filter(s => s.status === 'نشط');
        activeSources.forEach(source => {
            const option = document.createElement('option');
            option.value = source.name;
            option.textContent = source.name;
            select.appendChild(option);
        });
    }

    displayNews(newsToShow = this.news) {
        const container = document.getElementById('newsContainer');
        
        if (newsToShow.length === 0) {
            container.innerHTML = `
                <div class="no-news">
                    <i class="fas fa-newspaper"></i>
                    <p>لا توجد أخبار تطابق معايير البحث</p>
                </div>
            `;
            return;
        }

        container.innerHTML = newsToShow.map(news => this.createNewsCard(news)).join('');
    }

    createNewsCard(news) {
        const tagsHtml = news.tags && news.tags.length > 0 
            ? `<div class="news-tags">${news.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>`
            : '';

        const urlHtml = news.url 
            ? `<a href="${news.url}" target="_blank" class="btn btn-secondary"><i class="fas fa-external-link-alt"></i> رابط الخبر</a>`
            : '';

        return `
            <div class="news-item">
                <div class="news-header">
                    <div>
                        <h3 class="news-title">${news.title}</h3>
                        <div class="news-meta">
                            <span><i class="fas fa-calendar"></i> ${news.date}</span>
                            <span><i class="fas fa-clock"></i> ${news.time}</span>
                            <span><i class="fas fa-newspaper"></i> ${news.source}</span>
                            <span><i class="fas fa-tag"></i> ${news.category}</span>
                        </div>
                    </div>
                </div>
                <div class="news-content">${news.content}</div>
                ${tagsHtml}
                <div class="news-actions">
                    ${urlHtml}
                    <button onclick="newsMonitor.openModal(${news.id})" class="btn btn-secondary">
                        <i class="fas fa-edit"></i> تحرير
                    </button>
                    <button onclick="newsMonitor.deleteNews(${news.id})" class="btn btn-danger">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
    }

    filterNews() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const sourceFilter = document.getElementById('sourceFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;

        const filtered = this.news.filter(news => {
            const matchesSearch = !searchTerm || 
                news.title.toLowerCase().includes(searchTerm) ||
                news.content.toLowerCase().includes(searchTerm) ||
                (news.tags && news.tags.some(tag => tag.toLowerCase().includes(searchTerm)));
            
            const matchesCategory = !categoryFilter || news.category === categoryFilter;
            const matchesSource = !sourceFilter || news.source === sourceFilter;
            const matchesDate = !dateFilter || news.date === dateFilter;

            return matchesSearch && matchesCategory && matchesSource && matchesDate;
        });

        this.displayNews(filtered);
    }

    clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('sourceFilter').value = '';
        document.getElementById('dateFilter').value = '';
        this.displayNews();
    }

    showScrapingHelp() {
        const helpModal = document.createElement('div');
        helpModal.className = 'modal';
        helpModal.style.display = 'block';
        helpModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>💡 كيفية جلب الأخبار</h2>
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                </div>
                <div style="padding: 20px;">
                    <h3>🌐 المواقع المتاحة:</h3>
                    <div style="margin: 15px 0;">
                        ${this.sources.map(source => `
                            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                                <strong>${source.name}</strong><br>
                                <a href="${source.website}" target="_blank" style="color: #007bff;">
                                    ${source.website}
                                </a>
                            </div>
                        `).join('')}
                    </div>
                    
                    <h3>📝 طريقة الإضافة:</h3>
                    <ol style="margin: 15px 0; padding-right: 20px;">
                        <li>افتح أحد المواقع أعلاه</li>
                        <li>انسخ عنوان الخبر والمحتوى</li>
                        <li>ارجع للموقع واضغط "إضافة خبر جديد"</li>
                        <li>الصق المحتوى في النموذج</li>
                        <li>اختر المصدر من القائمة</li>
                        <li>احفظ الخبر</li>
                    </ol>
                    
                    <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <strong>💡 نصيحة:</strong> يمكنك فتح المواقع في تبويبات منفصلة لتسهيل النسخ واللصق
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(helpModal);
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        
        let bgColor = '#4CAF50';
        if (type === 'error') bgColor = '#dc3545';
        if (type === 'warning') bgColor = '#ffc107';
        if (type === 'info') bgColor = '#17a2b8';
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            max-width: 400px;
            word-wrap: break-word;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, type === 'error' ? 5000 : 3000);
    }
}

// تشغيل التطبيق
const newsMonitor = new NewsMonitor();
